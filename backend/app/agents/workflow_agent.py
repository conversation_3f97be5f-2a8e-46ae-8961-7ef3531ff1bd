from google.adk.agents import Agent, LlmAgent, SequentialAgent, ParallelAgent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.agent_tool import AgentTool
from google.adk.planners import BuiltInPlanner
from google.genai import types
from agents.guardrail import block_keyword_guardrail
from agents.sub_agents import (
    incident_manager_agent,
    log_analytics_agent,
    preference_agent,
    report_agent,
    root_cause_analyzer,
    runbook_generator_agent,
    time_agent,
)

from . import prompt

# AGENT_MODEL = "ollama/qwen3:4b"
AGENT_MODEL = "gemini/gemini-2.0-flash"
# AGENT_MODEL = "gemini/gemini-2.5-flash-preview-05-20"

# info_gatherer_agent = ParallelAgent(
#     name="InfoGathererAgent",
#     sub_agents=[log_analytics_agent, incident_manager_agent],
#     description="Gathers information from logs and incident management systems to assist in incident resolution.",
# )

final_agent = LlmAgent(
    name="IncidentResolutionAgent",
    model=LiteLlm(AGENT_MODEL),
    description="An agent that generates the final response based on the results from the workflow agent.",
    instruction="""
        You are an AI assistant designed to help Site Reliability Engineers (SREs) quickly resolve incidents by analyzing root causes and generating runbooks.
        Your role is to synthesize the information gathered by the previous agents and provide a comprehensive response that includes:
        1. **Root Cause**: A clear identification of the root cause of the incident.
        2. Steps to Resolve: A detailed set of actionable steps to resolve the incident.

        Information gathered by previous agents includes:
        {ai_analysis}

        {runbook}
        """,
)
workflow_agent = SequentialAgent(
    name="IncidentWorkflowAgent",
    sub_agents=[root_cause_analyzer, runbook_generator_agent, final_agent],
    description="Executes a sequence of agents to handle incident management tasks. The workflow includes root cause analysis followed by runbook generation and final response generation.",
)


main_agent = Agent(
    name="ai_assistant",
    model=LiteLlm(AGENT_MODEL),
    description="You are an AI based incident assistant. Your role is to assist the user in helping them identify the root cause and steps to resolve the incident, utilizing available tools.",
    instruction="""
You are a highly capable AI assistant designed to help Site Reliability Engineers (SREs) to quickly resolve any issues, errors, or incidents that may arise in their systems. 
You should be following ReACT framework.
For each user request:
    1. REASON: Think step-by-step about what information you need
    2. ACT: Use available tools to gather information
    3. OBSERVE: Review the results from tools
    4. Repeat until you can provide a complete response

Your primary goal is to assist users in identifying the root cause of incidents and providing actionable steps to resolve them. 
You will utilize various tools and sub-agents to gather information, analyze data, and generate solutions.
""",
    tools=[
        AgentTool(agent=workflow_agent, skip_summarization=True),
    ],
    planner=BuiltInPlanner(
        thinking_config=types.ThinkingConfig(
            include_thoughts=True,  # capture intermediate reasoning
            thinking_budget=2048,  # tokens allocated for planning
        )
    ),
    #     name="ai_assistant",
    #     instruction=prompt.INSTRUCTION,
    #     sub_agents=[time_agent, preference_agent],
    #     tools=[
    #         AgentTool(agent=runbook_generator_agent, skip_summarization=False),
    #         AgentTool(agent=root_cause_analyzer, skip_summarization=False),
    #         AgentTool(agent=log_analytics_agent, skip_summarization=False),
    #         AgentTool(agent=incident_manager_agent, skip_summarization=False),
    #         AgentTool(agent=report_agent, skip_summarization=False),
    #     ],
    #     before_model_callback=block_keyword_guardrail,
)
